{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^tm-(.*)": "@/tmui/components/tm-$1/tm-$1.vue"
		}
	},
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "皖嫂一家亲",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/resume_treasure/index",
			"style": {
				"navigationBarTitleText": "简历宝",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/make_money/index",
			"style": {
				"navigationBarTitleText": "赚钱宝",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/withdraw/index",
			"style": {
				"navigationBarTitleText": "提现",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/amount_details/index",
			"style": {
				"navigationBarTitleText": "金额明细",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/reward_details/index",
			"style": {
				"navigationBarTitleText": "奖励明细",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/act/index",
			"style": {
				"navigationBarTitleText": "最新活动"
			}
		},
		{
			"path": "pages/act_details/index",
			"style": {
				"navigationBarTitleText": "活动详情"
			}
		},
		{
			"path": "pages/signUp/index",
			"style": {
				"navigationBarTitleText": "活动报名"
			}
		},
		{
			"path": "pages/more_question/index",
			"style": {
				"navigationBarTitleText": "常见问题"
			}
		},
		{
			"path": "pages/news_details/index",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/news/index",
			"style": {
				"navigationBarTitleText": "热点新闻",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/business/index",
			"style": {
				"navigationBarTitleText": "查看名片",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/resume/index",
			"style": {
				"navigationBarTitleText": "皖嫂阿姨简历",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/view_evaluate/index",
			"style": {
				"navigationBarTitleText": "查看评价",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/evaluate/index",
			"style": {
				"navigationBarTitleText": "评价",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/info_basic/index",
			"style": {
				"navigationBarTitleText": "基础资料"
			}
		},
		// {
		// 	"path": "pages/info_idcard/index",
		// 	"style": {
		// 		"navigationBarTitleText": "身份证"
		// 	}
		// },
		{
			"path": "pages/info_workphoto/index",
			"style": {
				"navigationBarTitleText": "工作照"
			}
		},
		{
			"path": "pages/info_licensehoto/index",
			"style": {
				"navigationBarTitleText": "个人证照"
			}
		},
		{
			"path": "pages/info_schedule/index",
			"style": {
				"navigationBarTitleText": "档期管理"
			}
		},
		{
			"path": "pages/info_schedule_add/index",
			"style": {
				"navigationBarTitleText": "添加档期"
			}
		},
		{
			"path": "pages/login/phone",
			"style": {
				"navigationBarTitleText": "手机号登陆"
			}
		},
		{
			"path": "pages/jdb/index",
			"style": {
				"navigationBarTitleText": "接单宝",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/housekeeping/index",
			"style": {
				"navigationBarTitleText": "上户宝",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/clock/index",
			"style": {
				"navigationBarTitleText": "上下户打卡"
			}
		},
		{
			"path": "pages/clock_in/index",
			"style": {
				"navigationBarTitleText": "上户打卡",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/clock_out/index",
			"style": {
				"navigationBarTitleText": "下户打卡",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/confirm_clock_in/index",
			"style": {
				"navigationBarTitleText": "上户确认",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/confirm_clock_out/index",
			"style": {
				"navigationBarTitleText": "下户确认",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/ysqiangdan/index",
			"style": {
				"navigationBarTitleText": "皖嫂母婴接单大厅"
			}
		},
		{
			"path": "pages/grab/index",
			"style": {
				"navigationBarTitleText": "皖嫂家政抢单大厅"
			}
		},
		{
			"path": "pages/tuijian/index",
			"style": {
				"navigationBarTitleText": "推荐有礼",
				"navigationBarBackgroundColor": "#F54021",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ysduihuan/index",
			"style": {
				"navigationBarTitleText": "兑换优先上工机会",
				"navigationBarBackgroundColor": "#E33631",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/wsb_mx/index",
			"style": {
				"navigationBarTitleText": "皖嫂币明细",
				"navigationBarBackgroundColor": "#E33631",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/buywsb/index",
			"style": {
				"navigationBarTitleText": "购买皖嫂币",
				"navigationBarBackgroundColor": "#E33631",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/join/index",
			"style": {
				"navigationBarTitleText": "入驻一家亲",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/invite/index",
			"style": {
				"navigationBarTitleText": "一家亲推荐",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/invite_center/index",
			"style": {
				"navigationBarTitleText": "推荐中心",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/invite_poster/index",
			"style": {
				"navigationBarTitleText": "我的海报"
			}
		},
		{
			"path": "pages/train/index",
			"style": {
				"navigationBarTitleText": "我要培训"
			}
		},
		{
			"path": "pages/reserve/index",
			"style": {
				"navigationBarTitleText": "我要预约"
			}
		},
		{
			"path": "pages/intr/index",
			"style": {
				"navigationBarTitleText": "联系皖嫂"
			}
		},
		{
			"path": "pages/more_evaluate/index",
			"style": {
				"navigationBarTitleText": "更多评价"
			}
		},
		{
			"path": "pages/more_experList/index",
			"style": {
				"navigationBarTitleText": "工作经历"
			}
		},
		{
			"path": "pages/more_photo/index",
			"style": {
				"navigationBarTitleText": "阿姨相册"
			}
		},
		{
			"path": "pages/manual/index",
			"style": {
				"navigationBarTitleText": "月子护理服务手册",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/record_baby_feeding/index",
			"style": {
				"navigationBarTitleText": "婴儿科学喂养"
			}
		},
		{
			"path": "pages/record_baby_toilet/index",
			"style": {
				"navigationBarTitleText": "婴儿大小便记录"
			}
		},
		{
			"path": "pages/record_baby_nursing/index",
			"style": {
				"navigationBarTitleText": "婴儿护理记录"
			}
		},
		{
			"path": "pages/record_mother_diet/index",
			"style": {
				"navigationBarTitleText": "饮食记录"
			}
		},
		{
			"path": "pages/record_mother_nursing/index",
			"style": {
				"navigationBarTitleText": "宝妈护理记录"
			}
		},
		{
			"path": "pages/record_mother_other/index",
			"style": {
				"navigationBarTitleText": "其他记录"
			}
		},
		{
			"path": "pages/web/index"
		}
    ],
	"tabBar": {
		"color": "#fff",
		"height": "0px" ,
		"selectedColor": "#fff",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "一家亲"
			},
			{
				"pagePath": "pages/resume_treasure/index",
				"text": "简历宝"
			},
			{
				"pagePath": "pages/make_money/index",
				"text": "赚钱宝"
			},
			{
				"pagePath": "pages/jdb/index",
				"text": "接单宝"
			},
			{
				"pagePath": "pages/housekeeping/index",
				"text": "上户宝"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "皖嫂一家亲",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	"condition": {
		"current": 0,
		"list": [
			{
				"name": "",
				"path": "", 
				"query": ""
			}
		]
	}
}