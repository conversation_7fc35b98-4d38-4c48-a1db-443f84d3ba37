<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="218" src="/static/img/info_bg3.png"></tm-image>
				<view class="absolute flex-col flex-col-center-left" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff"
						label="宝妈护理记录"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff"
						label="SCIENTIFIC FEEDING"></tm-text>
				</view>
				<view class="topbox">
					<view class="box" @touchstart="showVoiceModal" @touchmove.stop.prevent="onTouchMove"
						@touchend="onTouchEnd">
						<view class="boxcontent">
							<image src="/static/img/voice.png" mode="widthFix" class="voice"></image>
							<view class="text1">语音记录</view>
						</view>
					</view>
				</view>
			</view>
			<view class="remark_area">
				<view class="form_item">
					<view class="form_item_title">母乳喂养：<text>{{ breastFeedingCount }}</text>次</view>
					<view class="form_item_add" @click="addBreastFeeding">加一次</view>
				</view>
				<view class="form_item">
					<view class="form_item_title">奶粉喂养：<text>{{ formulaFeedingCount }}</text>次</view>
					<view class="form_item_add" @click="addFormulaFeeding">加一次</view>
				</view>
				<view class="form_item form_item2">
					<view class="form_item_title">备注记录</view>
					<view class="fulled">
						<remark-list :list="remark" @delete="deleteRemark"></remark-list>
					</view>
				</view>
				<view class="form_item form_item2 noborder">
					<view class="form_item_title">备注：</view>
					<view class="remark-input">
						<textarea class="remark-textarea" v-model="remarkContent" :maxlength="-1"></textarea>
						<view class=" bottombox">
							<view class="box" @touchstart="showVoiceModal" @touchmove.stop.prevent="onTouchMove"
								@touchend="onTouchEnd">
								<view class="boxcontent">
									<image src="/static/img/voice.png" mode="widthFix" class="voice"></image>
									<view class="text1">语音记录</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="submit-btn" @click="submitRecord">提交</view>
			</view>
		</view>
		<tm-overlay v-model:show="show" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
			<view class="dkpopup dkpopup2">
				<image src="/static/img/yqtk.png" mode="widthFix" class="dkcg"></image>
				<view class="content content3">
					<view class="content_tit">奶粉喂养</view>
					<div class="pick_area">
						<div class="pick_item" :class="{ active: pick === 1 }" @click="pick = 1">10毫升</div>
						<div class="pick_item" :class="{ active: pick === 2 }" @click="pick = 2">20毫升</div>
						<div class="pick_item" :class="{ active: pick === 3 }" @click="pick = 3">30毫升</div>
						<div class="pick_item" :class="{ active: pick === 4 }">
							<input type="text" class="zdy" @focus="pick = 4" @input="changeMilk">
							<text v-if="milk && pick === 4" class="unit">毫升</text>
							<text class="placeholder" v-else>自定义</text>
						</div>
					</div>
				</view>
				<view class="confirm" @click="show = false">提交</view>
			</view>
		</tm-overlay>
		<!-- 录音弹窗组件 -->
		<voice-record-modal ref="voiceRecordModalRef" @cbResult="getCbResult"></voice-record-modal>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia'
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import remarkList from '@/components/remark-list/remark-list.vue'
import voiceRecordModal from '@/components/voice-record-modal/voice-record-modal.vue'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 响应式数据
interface RemarkItem {
	content: string;
	time: string;
	type: 'text' | 'voice';
	tempFilePath?: string;
	duration?: number;
}
const show = ref(false)
const pick = ref(1)
const milk = ref(0)
watch(pick, (newVal) => {
	if (pick === 1) {
		milk.value = 10
	} else if (pick === 2) {
		milk.value = 20
	} else if (pick === 3) {
		milk.value = 30
	}
}, {
	immediate: true
})
const changeMilk = (e) => {
	milk.value = e.detail.value
}
const breastFeedingCount = ref(3)
const formulaFeedingCount = ref(1)
const remarkContent = ref('')
const remark = ref<RemarkItem[]>([
	{
		content: '今天状态很好',
		time: '1749621666064',
		type: 'text'
	},
	{
		content: '语音：02:30',
		time: '1749621666064',
		type: 'voice',
		tempFilePath: '',
		duration: 150
	}
])

// 处理录音回调
const getCbResult = (e: any) => {
	if (!e.tempFilePath) return
	const now = new Date()
	remark.value.push({
		content: `语音：${e.showTime}`,
		time: new Date().getTime(),
		type: 'voice',
		tempFilePath: e.tempFilePath,
		duration: e.duration
	})
}
// 录音弹窗引用
const voiceRecordModalRef = ref(null)

// 显示录音弹窗
const showVoiceModal = () => {
	if (voiceRecordModalRef.value) {
		voiceRecordModalRef.value.showVoice()
	}
}

// 触摸移动事件
const onTouchMove = (e) => {
	if (voiceRecordModalRef.value) {
		voiceRecordModalRef.value.onTouchMove(e)
	}
}

// 触摸结束事件
const onTouchEnd = (e) => {
	if (voiceRecordModalRef.value) {
		voiceRecordModalRef.value.onTouchEnd(e)
	}
}


// 增加母乳喂养次数
const addBreastFeeding = () => {
	breastFeedingCount.value++
}

// 增加奶粉喂养次数
const addFormulaFeeding = () => {
	show.value = true
	// formulaFeedingCount.value++
}

// 删除备注记录
const deleteRemark = async (index: number) => {
	// 获取要删除的记录
	const itemToDelete = remark.value[index]
	remark.value.splice(index, 1)
	// 调用删除接口
	const res = await api.request.ajax({
		url: '/deleteremark',
		type: 'POST',
		data: {
			type: 1,
			item: JSON.stringify(itemToDelete)
		}
	});
}

// 提交记录
const submitRecord = async () => {
	if (remarkContent.value.trim() === '') return
	let item = {
		content: remarkContent.value,
		time: new Date().getTime(),
		type: 'text'
	}
	remark.value.push(item)
	remarkContent.value = ''
	const res = await api.request.ajax({
		url: '/addremark',
		type: 'POST',
		data: {
			type: 1,
			value: JSON.stringify(item)
		}
	});
}

</script>
<style lang="less">
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.topbox {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 50rpx;
	}

	.bottombox {
		position: absolute;
		z-index: 2;
		bottom: 0%;
		left: 50%;
		transform: translate(-50%, 50%);
	}

}
</style>