<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center overflow mt-16">
				<tm-image :width="688" :height="218" src="/static/img/info_bg2.png"></tm-image>
				<tm-image :width="159" :height="213" src="/static/img/info_people.png" class="absolute r-n10 b--10"></tm-image>
				<view class="absolute flex-col flex-col-center-left" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff"
						label="婴儿护理记录"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff"
						label="BABY CARE RECORDS"></tm-text>
				</view>
			</view>
			<view class="remark_area">
				<view class="form_item form_item2">
					<view class="form_item_title">
						体温
						<text class="form_item_title_tip">正常体温：36-37℃</text>
					</view>
					<view class="form_item_content">
						<view class="form_item_content_switch">
							<input type="text" class="input">
							<text class="unit">℃</text>
						</view>
						<view class="form_item_content_switch">
							<input type="text" class="input" placeholder="请输入">
							<text class="unit">℃</text>
						</view>
					</view>
				</view>
				<view class="form_item form_item2">
					<view class="form_item_title">
						呼吸
						<text class="form_item_title_tip">正常1分钟：40-60次</text>
					</view>
					<view class="form_item_content">
						<view class="form_item_content_switch">完成</view>
						<view class="form_item_content_switch">无需服务</view>
					</view>
				</view>
				<view class="form_item form_item2">
					<view class="form_item_title">
						呼吸
						<text class="form_item_title_tip">正常1分钟：40-60次</text>
					</view>
					<view class="form_item_content">
						<view class="form_item_content_switch">完成</view>
						<view class="form_item_content_switch">无需服务</view>
					</view>
				</view>
				<view class="submit-btn" @click="submitRecord">提交</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia'
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 响应式数据


// 提交记录
const submitRecord = async() => {
	if(remarkContent.value.trim() === '') return
	let item = {
		content: remarkContent.value,
		time: new Date().getTime(),
		type: 'text'
	}
	remark.value.push(item)
	remarkContent.value = ''
	const res = await api.request.ajax({
		url: '/addremark',
		type: 'POST',
		data:{
			type:1,
			value: JSON.stringify(item)
		}
	});
}

</script>
<style lang="less">
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.topbox {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 50rpx;
	}

	.bottombox {
		position: absolute;
		z-index: 2;
		bottom: 0%;
		left: 50%;
		transform: translate(-50%, 50%);
	}
}
</style>