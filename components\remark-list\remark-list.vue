<template>
	<view class="remark-list">
		<view class="remark-item" v-for="(item, index) in list" :key="index">
			<view class="remark-left">
				<view class="remark-content">
					<template v-if="item.type === 'text'">
						{{ item.content }}
					</template>
					<template v-else>
						<view class="duration">{{ item.content }}</view>
						<view class="play-icon" :class="{'playing': currentPlayingIndex === index}" @click="playVoice(index)"></view>
					</template>
				</view>
				<view class="remark-time">{{ formatTime(item.time) }}</view>
			</view>
			<view class="remark-delete" @click="deleteRemark(index)">
				<view class="delete-icon">
					<view class="delete-icon-inner"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, onUnmounted,computed } from 'vue'

interface RemarkItem {
	content: string;
	time: string;
	type: 'text' | 'voice';
	tempFilePath?: string;
	duration?: number;
}
//用vue3的computed将时间戳过滤成${year}-${month}-${day} ${hour}:${minute}:${second}
const formatTime = computed(()=>{
	return (time:string)=>{
		const date = new Date(Number(time))
		const year = date.getFullYear()
		const month = (date.getMonth() + 1)>9?(date.getMonth() + 1):'0'+(date.getMonth() + 1)
		const day = date.getDate()>9?date.getDate():'0'+date.getDate()
		const hour = date.getHours()>9?date.getHours():'0'+date.getHours()
		const minute = date.getMinutes()>9?date.getMinutes():'0'+date.getMinutes()
		const second = date.getSeconds()>9?date.getSeconds():'0'+date.getSeconds()
		return `${year}-${month}-${day} ${hour}:${minute}:${second}`
	}
})


const props = defineProps<{
	list: RemarkItem[];
}>()

const emit = defineEmits<{
	delete: [index: number]
}>()

const currentPlayingIndex = ref(-1)
const innerAudioContext = ref<any>(null)

// 播放录音
const playVoice = (index: number) => {
	const item = props.list[index]
	if (!item.tempFilePath) return
	
	// 如果正在播放，先停止
	if (currentPlayingIndex.value === index) {
		innerAudioContext.value?.stop()
		currentPlayingIndex.value = -1
		return
	}
	
	// 如果有其他正在播放，先停止
	if (currentPlayingIndex.value !== -1) {
		innerAudioContext.value?.stop()
	}
	
	// 创建新的音频上下文
	innerAudioContext.value = uni.createInnerAudioContext()
	innerAudioContext.value.autoplay = true
	innerAudioContext.value.src = item.tempFilePath
	
	// 监听播放结束
	innerAudioContext.value.onEnded(() => {
		currentPlayingIndex.value = -1
	})
	
	currentPlayingIndex.value = index
}

// 删除备注
const deleteRemark = (index: number) => {
	emit('delete', index)
}

// 组件销毁时清理音频上下文
onUnmounted(() => {
	if (innerAudioContext.value) {
		innerAudioContext.value.destroy()
	}
})
</script>

<style lang="less">
.remark-list {
	margin-top: 20rpx;
	padding: 0 14rpx 0 22rpx;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	.remark-item {
		margin-top: 34rpx;
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		// max-width: 80%;

		.remark-left {
			flex: 1;
		}

		.remark-delete {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 16rpx;
			margin-top: 16rpx;
			flex-shrink: 0;

			.delete-icon {
				width: 44rpx;
				height: 44rpx;
				background: linear-gradient(135deg, #ff6b6b, #ee5a52);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				position: relative;
				transition: all 0.2s ease;
				box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);

				&:hover {
					transform: scale(1.1);
					box-shadow: 0 6rpx 16rpx rgba(255, 107, 107, 0.4);
				}

				&:active {
					transform: scale(0.95);
					background: linear-gradient(135deg, #ee5a52, #dc4c41);
				}

				.delete-icon-inner {
					width: 20rpx;
					height: 20rpx;
					position: relative;

					&::before,
					&::after {
						content: '';
						position: absolute;
						width: 20rpx;
						height: 3rpx;
						background-color: #fff;
						border-radius: 2rpx;
						top: 50%;
						left: 50%;
						transform-origin: center;
					}

					&::before {
						transform: translate(-50%, -50%) rotate(45deg);
					}

					&::after {
						transform: translate(-50%, -50%) rotate(-45deg);
					}
				}
			}
		}

		.remark-content{
			background-color: #F0F0F0;
			min-height: 90rpx;
			padding: 16rpx 32rpx;
			border-radius: 16rpx;
			font-size: 28rpx;
			line-height: 40rpx;
			display: flex;
			align-items: center;
			.play-icon {
				width: 64rpx;
				height: 64rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				border-radius: 50%;
				margin-left: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				cursor: pointer;
				flex-shrink: 0;
				
				&.playing {
					display: flex;
					justify-content: center;
					
					&::after {
						content: '';
						width: 6rpx;
						height: 32rpx;
						background-color: #fff;
						margin: 0 4rpx;
					}
					
					&::before {
						content: '';
						width: 6rpx;
						height: 32rpx;
						background-color: #fff;
						margin: 0 4rpx;
					}
				}
				
				&:not(.playing)::after {
					content: '';
					width: 0;
					height: 0;
					border-top: 14rpx solid transparent;
					border-bottom: 14rpx solid transparent;
					border-left: 20rpx solid #fff;
					position: absolute;
				}
			}
		}
		.remark-time {
			margin-top: 8rpx;
			font-size: 22rpx;
			color: #C6C6C6;
		}
	}
}
</style> 